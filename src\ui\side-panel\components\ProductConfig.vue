<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// Props
const props = defineProps<{
  configForm: {
    minStock: number
    fixedStock: number | null
    enableDeduplication: boolean
    titlePrefix: string
    titleSuffix: string
    uploadInterval: number
    priceMultiplier: number
    collectDetails: string[]
    collectSku: boolean
    externalLink: boolean
    defaultSize: {
      length: number
      width: number
      height: number
      weight: number
    }
    filterProhibited: boolean
    prohibitedWords: string
    enableTranslation: boolean
    translationService: string
  }
}>()

// Emits
const emit = defineEmits<{
  'update:configForm': [value: typeof props.configForm]
  'save-config': []
}>()

// 更新表单数据
const updateForm = (key: string, value: any) => {
  const newForm = { ...props.configForm, [key]: value }
  emit('update:configForm', newForm)
}

// 更新嵌套对象
const updateNestedForm = (parentKey: string, childKey: string, value: any) => {
  const newForm = {
    ...props.configForm,
    [parentKey]: {
      ...props.configForm[parentKey as keyof typeof props.configForm],
      [childKey]: value
    }
  }
  emit('update:configForm', newForm)
}

const handleSave = () => {
  emit('save-config')
}
</script>

<template>
  <div class="product-config-container">
    <a-form
      :model="configForm"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      class="space-y-4"
      @finish="handleSave"
    >
      <!-- 库存设置 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          title="📦 库存设置"
          class="mb-4"
        >
          <a-form
            layout="horizontal"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item
              label="最小库存"
              name="minStock"
              :rules="[{ required: true, message: '请输入最小库存' }]"
            >
              <a-input-number
                :value="configForm.minStock"
                :min="0"
                placeholder="请输入最小库存"
                style="width: 100%"
                @change="(value) => updateForm('minStock', value)"
              >
                <template #addonAfter>件</template>
              </a-input-number>
              <div class="mt-1 text-xs text-gray-500">
                货盘库存低于配置最小库存，将不发布上品
              </div>
            </a-form-item>

            <a-form-item label="固定上品库存">
              <a-input-number
                :value="configForm.fixedStock"
                :min="0"
                placeholder="不设置则为空"
                style="width: 100%"
                @change="(value) => updateForm('fixedStock', value)"
              >
                <template #addonAfter>件</template>
              </a-input-number>
              <div class="mt-1 text-xs text-gray-500">
                设置固定后，将以设置的库存数为上品库存
              </div>
            </a-form-item>

            <a-form-item label="去重设置">
              <a-switch
                :checked="configForm.enableDeduplication"
                checked-children="启用"
                un-checked-children="关闭"
                @change="(checked) => updateForm('enableDeduplication', checked)"
              />
              <div class="mt-1 text-xs text-gray-500">
                启用后，同个店铺同个货盘如上次已上过品，本次将不执行上品
              </div>
            </a-form-item>
          </a-form>
        </a-card>
      </a-form-item>

      <!-- 标题设置 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          title="📝 标题设置"
          class="mb-4"
        >
          <a-form
            layout="horizontal"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="标题前缀">
              <a-input
                :value="configForm.titlePrefix"
                :maxlength="100"
                placeholder="请输入关键词(选填)"
                show-count
                @change="(e) => updateForm('titlePrefix', e.target.value)"
              />
            </a-form-item>

            <a-form-item label="标题后缀">
              <a-input
                :value="configForm.titleSuffix"
                :maxlength="170"
                placeholder="请输入关键词(选填)"
                show-count
                @change="(e) => updateForm('titleSuffix', e.target.value)"
              />
            </a-form-item>
          </a-form>
          <div class="text-xs text-gray-500 px-4 pb-2">
            选填，标题前缀和标题后缀，将分别添加到标题前面和后面
          </div>
        </a-card>
      </a-form-item>

      <!-- 价格和时间设置 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          title="💰 价格和时间设置"
          class="mb-4"
        >
          <a-form
            layout="horizontal"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item
              label="上品价格上浮"
              name="priceMultiplier"
              :rules="[{ required: true, message: '请输入价格倍数' }]"
            >
              <a-input-number
                :value="configForm.priceMultiplier"
                :min="1"
                :step="0.1"
                placeholder="请输入倍数"
                style="width: 100%"
                @change="(value) => updateForm('priceMultiplier', value)"
              >
                <template #addonBefore>默认</template>
                <template #addonAfter>倍</template>
              </a-input-number>
            </a-form-item>

            <a-form-item label="上品时间间隔">
              <a-input-number
                :value="configForm.uploadInterval"
                :min="0"
                placeholder="默认为0"
                style="width: 100%"
                @change="(value) => updateForm('uploadInterval', value)"
              >
                <template #addonAfter>秒</template>
              </a-input-number>
              <div class="mt-1 text-xs text-gray-500">
                上完一个品后，下个品的时间间隔，一般默认为0
              </div>
            </a-form-item>
          </a-form>
        </a-card>
      </a-form-item>

      <!-- 采集设置 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          title="🌐 采集设置"
          class="mb-4"
        >
          <a-form
            layout="horizontal"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="采集详情需要">
              <a-checkbox-group
                :value="configForm.collectDetails"
                @change="(value) => updateForm('collectDetails', value)"
              >
                <a-checkbox value="title">文字</a-checkbox>
                <a-checkbox value="img">详情图</a-checkbox>
              </a-checkbox-group>
            </a-form-item>

            <a-form-item label="采集SKU货号">
              <a-switch
                :checked="configForm.collectSku"
                checked-children="启用"
                un-checked-children="关闭"
                @change="(checked) => updateForm('collectSku', checked)"
              />
              <div class="mt-1 text-xs text-gray-500">
                上品时自动把商品采集的货号填入SKU货号中
              </div>
            </a-form-item>

            <a-form-item label="站外产品链接">
              <a-switch
                :checked="configForm.externalLink"
                checked-children="启用"
                un-checked-children="关闭"
                @change="(checked) => updateForm('externalLink', checked)"
              />
              <div class="mt-1 text-xs text-gray-500">
                自动把商品采集的链接填入站外产品链接（仅对TEMU采集模式有效）
              </div>
            </a-form-item>
          </a-form>
        </a-card>
      </a-form-item>

      <!-- 默认尺寸设置 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          title="📏 默认尺寸设置"
          class="mb-4"
        >
          <div class="px-4 pt-2">
            <!-- 一行显示四个尺寸字段 -->
            <a-row
              :gutter="12"
              class="mb-3"
            >
              <a-col :span="6">
                <div class="text-sm font-medium mb-1">长边(cm)</div>
                <a-input-number
                  :value="configForm.defaultSize.length"
                  :min="0"
                  placeholder="长边"
                  size="small"
                  style="width: 100%"
                  @change="(value) => updateNestedForm('defaultSize', 'length', value)"
                />
              </a-col>
              <a-col :span="6">
                <div class="text-sm font-medium mb-1">宽度(cm)</div>
                <a-input-number
                  :value="configForm.defaultSize.width"
                  :min="0"
                  placeholder="宽度"
                  size="small"
                  style="width: 100%"
                  @change="(value) => updateNestedForm('defaultSize', 'width', value)"
                />
              </a-col>
              <a-col :span="6">
                <div class="text-sm font-medium mb-1">高度(cm)</div>
                <a-input-number
                  :value="configForm.defaultSize.height"
                  :min="0"
                  placeholder="高度"
                  size="small"
                  style="width: 100%"
                  @change="(value) => updateNestedForm('defaultSize', 'height', value)"
                />
              </a-col>
              <a-col :span="6">
                <div class="text-sm font-medium mb-1">重量(g)</div>
                <a-input-number
                  :value="configForm.defaultSize.weight"
                  :min="0"
                  placeholder="重量"
                  size="small"
                  style="width: 100%"
                  @change="(value) => updateNestedForm('defaultSize', 'weight', value)"
                />
              </a-col>
            </a-row>

            <div class="text-xs text-gray-500 pb-2">
              如果商品尺寸为空时，将会使用默认尺寸
            </div>
          </div>
        </a-card>
      </a-form-item>

      <!-- 违禁词过滤 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          title="🚫 违禁词过滤"
          class="mb-4"
        >
          <a-form
            layout="horizontal"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="违禁词过滤">
              <a-switch
                :checked="configForm.filterProhibited"
                checked-children="启用"
                un-checked-children="关闭"
                @change="(checked) => updateForm('filterProhibited', checked)"
              />
            </a-form-item>

            <a-form-item
              v-if="configForm.filterProhibited"
              label="违禁词列表"
            >
              <a-textarea
                :value="configForm.prohibitedWords"
                :rows="3"
                placeholder="请输入违禁词，每行一个..."
                @change="(e) => updateForm('prohibitedWords', e.target.value)"
              />
              <div class="mt-1 text-xs text-gray-500">
                启用后，系统将自动过滤包含违禁词的商品标题和描述
              </div>
            </a-form-item>
          </a-form>
        </a-card>
      </a-form-item>

      <!-- 翻译设置 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          title="🌍 翻译设置"
          class="mb-4"
        >
          <a-form
            layout="horizontal"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="自动翻译">
              <a-switch
                :checked="configForm.enableTranslation"
                checked-children="启用"
                un-checked-children="关闭"
                @change="(checked) => updateForm('enableTranslation', checked)"
              />
            </a-form-item>

            <a-form-item
              v-if="configForm.enableTranslation"
              label="翻译服务"
            >
              <a-select
                :value="configForm.translationService"
                placeholder="请选择翻译服务"
                style="width: 100%"
                @change="(value) => updateForm('translationService', value)"
              >
                <a-select-option value="google">Google 翻译</a-select-option>
                <a-select-option value="baidu">百度翻译</a-select-option>
                <a-select-option value="youdao">有道翻译</a-select-option>
              </a-select>
              <div class="mt-1 text-xs text-gray-500">
                启用后，系统将自动翻译商品标题和描述到目标语言
              </div>
            </a-form-item>
          </a-form>
        </a-card>
      </a-form-item>

      <!-- 保存按钮 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <div class="flex justify-center pt-4">
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            class="px-12 h-10"
          >
            ⚙️ 保存上品配置
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped>
.product-config-container {
  width: 100%;
  min-height: auto; /* 自适应内容高度 */
  padding: 0; /* 父容器已设置padding */
  margin: 0 auto;
  background: transparent; /* 使用父容器背景 */
  border-radius: 0; /* 避免重复圆角 */
  overflow: visible; /* 让最外层容器处理滚动 */
}

/* 滚动条样式由父容器 step-panel 处理 */

.product-config-container :deep(.ant-form-item-label > label) {
  font-weight: 600;
  font-size: 14px;
}

.product-config-container :deep(.ant-form-item) {
  margin-bottom: var(--space-lg);
}

/* 表单布局优化 - 居中对齐 */
.product-config-container :deep(.ant-form) {
  max-width: 100%;
}

/* 卡片内容居中 */
.product-config-container :deep(.ant-card) {
  margin-bottom: var(--space-lg);
}

/* 表单项标签对齐 */
.product-config-container :deep(.ant-form-item-label) {
  text-align: left;
  padding-right: var(--space-md);
}

.product-config-container :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

.product-config-container :deep(.ant-card-small > .ant-card-head) {
  min-height: 40px;
}

.product-config-container :deep(.ant-card-small .ant-card-head-title) {
  font-size: 14px;
  font-weight: 600;
}

.product-config-container :deep(.ant-card-body) {
  padding: 16px;
}

.product-config-container :deep(.ant-select) {
  font-size: 14px;
}

.product-config-container :deep(.ant-input) {
  font-size: 14px;
}

.product-config-container :deep(.ant-input-number) {
  font-size: 14px;
}

/* 紧凑布局优化 */
.product-config-container :deep(.ant-form-horizontal .ant-form-item-label) {
  padding-right: 12px;
}

.product-config-container :deep(.ant-form-horizontal .ant-form-item-control) {
  flex: 1;
}

/* 保存按钮区域样式 */
.product-config-container :deep(.flex.justify-center) {
  margin-top: var(--space-xl);
  margin-bottom: var(--space-xl);
  padding: var(--space-lg) 0;
  border-top: 1px solid var(--border-color);
  background: var(--bg-container);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 按钮样式优化 */
.product-config-container :deep(.ant-btn-primary) {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
  font-weight: var(--font-medium);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-config-container :deep(.ant-btn-primary:hover) {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
</style>
