import amazonDataService from '../../services/amazon/amazonDataService'
import configStorageService from '../../services/configStorageService'
import imageProcessingService from '../../services/imageProcessingService'
import amazonCollectionController from '../../services/amazon/amazonCollectionController'
import pushService from '../../services/pushService'

// 采集状态管理
let isCollecting = false
let collectedCount = 0

// 创建批量采集按钮（页面右侧中间偏上）
function createBatchCollectionButton(): HTMLElement {
  const button = document.createElement('button')
  button.className = 'amazon-batch-collect-btn'
  button.type = 'button'

  // 获取匹配的商品数量
  const productCount = getMatchedProductCount()

  button.style.cssText = `
    position: fixed;
    top: 35vh;
    right: 20px;
    z-index: 9999;
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    background: #ff4d4f;
    color: white;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
  `

  // 创建图标
  const icon = document.createElement('div')
  icon.innerHTML = `
    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
      <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
    </svg>
  `
  button.appendChild(icon)

  // 创建文字和数量
  const textContainer = document.createElement('div')
  textContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
  `

  const mainText = document.createElement('span')
  mainText.textContent = '[HJ]批量采集'
  mainText.style.fontSize = '14px'

  const countText = document.createElement('span')
  countText.className = 'product-count'
  countText.textContent = `${productCount} 个商品`
  countText.style.cssText = `
    font-size: 12px;
    opacity: 0.9;
    font-weight: normal;
  `

  textContainer.appendChild(mainText)
  textContainer.appendChild(countText)
  button.appendChild(textContainer)

  // 添加点击事件
  button.addEventListener('click', handleCollectionClick)

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.transform = 'scale(1.05)'
    button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.3)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.transform = 'scale(1)'
    button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)'
  })

  return button
}

// 获取匹配的商品数量
function getMatchedProductCount(): number {
  const productElements = document.querySelectorAll('[data-asin]:not(.amazon-collected)')
  return productElements.length
}

// 创建单品采集按钮（固定显示在商品图片右上角）
function createProductCollectionButton(productElement: Element): HTMLElement {
  const button = document.createElement('div')
  button.className = 'amazon-product-collect-btn'
  button.style.cssText = `
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    z-index: 10;
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
  `
  button.textContent = '[HJ]单品采集'

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.background = '#ff7875'
    button.style.transform = 'scale(1.05)'
    button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.4)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.background = '#ff4d4f'
    button.style.transform = 'scale(1)'
    button.style.boxShadow = '0 2px 6px rgba(255, 77, 79, 0.3)'
  })

  // 添加点击事件
  button.addEventListener('click', (e) => {
    e.preventDefault()
    e.stopPropagation()
    handleSingleProductCollection(productElement)
  })

  return button
}

// 处理采集按钮点击
async function handleCollectionClick() {
  if (isCollecting) {
    showNotification('正在采集中，请稍候...', 'warning')
    return
  }

  try {
    // 检查配置完整性
    const configCheck = await configStorageService.isConfigComplete()
    if (!configCheck.isComplete) {
      showConfigurationPrompt(configCheck.missingFields)
      return
    }

    // 增强页面类型检测
    const pageType = detectAmazonPageType()
    console.info('[AmazonCollector] 检测到页面类型:', pageType)

    switch (pageType) {
      case 'search':
        await handleBatchCollection()
        break
      case 'product':
        await handleSingleProductCollection()
        break
      case 'category':
        showNotification('分类页面暂不支持采集，请进入搜索结果页面', 'warning')
        break
      default:
        showNotification('当前页面不支持采集，请访问Amazon商品页面或搜索结果页面', 'warning')
    }
  } catch (error) {
    console.error('[AmazonCollector] 采集失败:', error)
    const errorMessage = getDetailedErrorMessage(error)
    showNotification(`采集失败: ${errorMessage}`, 'error')
  }
}

// 增强的页面类型检测
function detectAmazonPageType(): 'search' | 'product' | 'category' | 'unknown' {
  const url = window.location.href
  const pathname = window.location.pathname

  // 检测商品详情页
  if (url.includes('/dp/') || url.includes('/gp/product/')) {
    return 'product'
  }

  // 检测搜索结果页
  if (url.includes('/s?') || url.includes('/s/') || pathname.startsWith('/s')) {
    return 'search'
  }

  // 检测分类页面
  if (pathname.includes('/b/') || pathname.includes('/gp/browse.html')) {
    return 'category'
  }

  return 'unknown'
}

// 获取详细错误信息
function getDetailedErrorMessage(error: any): string {
  if (error instanceof Error) {
    // 网络错误
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return '网络连接失败，请检查网络连接后重试'
    }

    // 配置错误
    if (error.message.includes('配置') || error.message.includes('config')) {
      return '配置信息不完整，请检查店小秘配置'
    }

    // ASIN提取错误
    if (error.message.includes('ASIN') || error.message.includes('asin')) {
      return '无法识别Amazon商品，请确保在正确的商品页面'
    }

    // 权限错误
    if (error.message.includes('permission') || error.message.includes('权限')) {
      return '权限不足，请刷新页面后重试'
    }

    return error.message
  }

  return '未知错误，请重试'
}

// 处理单品采集
async function handleSingleProductCollection(productElement?: Element) {
  let progressNotification: any = null

  try {
    isCollecting = true
    updateButtonState()

    // 显示进度通知
    progressNotification = showProgressNotification('正在采集商品数据...', 0)

    // 构造Amazon产品链接
    const productUrl = await extractProductUrl(productElement)
    console.info('[AmazonCollector] 提取的产品链接:', productUrl)

    // 更新进度
    updateProgressNotification(progressNotification, '正在分析商品类型...', 20)

    // 预检测商品类型（单体/多变体）
    const productType = await detectProductType(productUrl)
    console.info('[AmazonCollector] 检测到商品类型:', productType)

    // 更新进度
    updateProgressNotification(progressNotification, '正在提取商品数据...', 40)

    // 发送消息到background.js进行数据提取和组装
    const result = await chrome.runtime.sendMessage({
      action: 'EXTRACT_AND_ASSEMBLE_AMAZON_DATA',
      productUrl: productUrl,
      productType: productType // 传递商品类型信息
    })

    if (!result.success) {
      throw new Error(result.error || '数据处理失败')
    }

    // 更新进度
    updateProgressNotification(progressNotification, '正在组装数据...', 80)

    console.info('[AmazonCollector] 收到background.js处理结果:', result)

    // 验证数据完整性
    validateCollectedData(result)

    // 更新进度
    updateProgressNotification(progressNotification, '采集完成！', 100)

    // 关闭进度通知
    setTimeout(() => {
      if (progressNotification) {
        progressNotification.remove()
      }
    }, 1000)

    // 显示数据预览弹窗
    showDataPreviewModal(result.spuData, result.skuDataList || [], result.dianxiaomiData)

    collectedCount++
    updateProductCount()

    // 根据商品类型显示不同的成功消息
    const successMessage = productType === 'multi-variant'
      ? `多变体商品采集完成，共${result.skuDataList?.length || 0}个SKU`
      : '单体商品采集完成'

    showNotification(successMessage, 'success')

    // 如果是从产品列表中采集，标记该产品已采集
    if (productElement) {
      markProductAsCollected(productElement, productType)
    }

  } catch (error) {
    console.error('[AmazonCollector] 单品采集失败:', error)

    // 关闭进度通知
    if (progressNotification) {
      progressNotification.remove()
    }

    const errorMessage = getDetailedErrorMessage(error)
    showNotification(`单品采集失败: ${errorMessage}`, 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 提取产品URL
async function extractProductUrl(productElement?: Element): Promise<string> {
  if (productElement) {
    // 从搜索页面的商品卡片中提取ASIN
    const asin = productElement.getAttribute('data-asin')
    if (!asin) {
      throw new Error('无法从商品卡片获取ASIN')
    }
    return `https://www.amazon.com/dp/${asin}`
  } else {
    // 从当前页面URL中提取
    const currentUrl = window.location.href
    const asinMatch = currentUrl.match(/\/dp\/([A-Z0-9]{10})/)
    if (!asinMatch) {
      throw new Error('无法从当前页面URL提取ASIN，请确保在Amazon商品页面')
    }
    return `https://www.amazon.com/dp/${asinMatch[1]}`
  }
}

// 检测商品类型（单体/多变体）
async function detectProductType(productUrl: string): Promise<'single' | 'multi-variant'> {
  try {
    // 如果是当前页面，可以直接检测DOM
    if (window.location.href.includes(productUrl.split('/dp/')[1])) {
      return detectProductTypeFromDOM()
    }

    // 否则返回默认类型，让background.js进行详细检测
    return 'single'
  } catch (error) {
    console.warn('[AmazonCollector] 商品类型检测失败，使用默认类型:', error)
    return 'single'
  }
}

// 从DOM检测商品类型
function detectProductTypeFromDOM(): 'single' | 'multi-variant' {
  // 检测变体选择器
  const variationSelectors = [
    '#variation_color_name',
    '#variation_size_name',
    '#variation_style_name',
    '[data-feature-name="variations"]',
    '.a-button-group[data-a-button-group-name]',
    '.swatches'
  ]

  for (const selector of variationSelectors) {
    const element = document.querySelector(selector)
    if (element && element.children.length > 1) {
      console.info('[AmazonCollector] 检测到变体选择器:', selector)
      return 'multi-variant'
    }
  }

  return 'single'
}

// 验证采集的数据
function validateCollectedData(result: any): void {
  if (!result.spuData) {
    throw new Error('SPU数据缺失')
  }

  if (!result.spuData.asin || result.spuData.asin === 'UNKNOWN') {
    throw new Error('无法获取有效的ASIN')
  }

  if (!result.skuDataList || result.skuDataList.length === 0) {
    console.warn('[AmazonCollector] SKU数据为空，可能是单体商品')
  }

  if (!result.dianxiaomiData) {
    throw new Error('店小秘格式数据组装失败')
  }

  console.info('[AmazonCollector] 数据验证通过')
}

// 处理批量采集
async function handleBatchCollection() {
  let batchProgressNotification: any = null

  try {
    isCollecting = true
    updateButtonState()

    // 获取当前页面的所有产品
    const productElements = document.querySelectorAll('[data-asin]:not(.amazon-collected)')

    if (productElements.length === 0) {
      showNotification('当前页面没有找到可采集的商品', 'warning')
      return
    }

    // 显示批量采集进度
    batchProgressNotification = showBatchProgressNotification(productElements.length)

    let successCount = 0
    let failCount = 0
    const collectedData: any[] = []

    // 限制并发数量，避免过多请求
    const maxConcurrent = 2
    const batchSize = Math.min(maxConcurrent, productElements.length)

    for (let i = 0; i < productElements.length; i += batchSize) {
      const batch = Array.from(productElements).slice(i, i + batchSize)

      // 并发处理当前批次
      const batchPromises = batch.map(async (productElement, batchIndex) => {
        const globalIndex = i + batchIndex
        const asin = productElement.getAttribute('data-asin')

        if (!asin) return { success: false, error: 'No ASIN found' }

        try {
          updateBatchProgressNotification(
            batchProgressNotification,
            `正在采集第 ${globalIndex + 1}/${productElements.length} 个商品 (ASIN: ${asin})...`,
            globalIndex,
            productElements.length
          )

          // 实际进行数据采集
          const productUrl = `https://www.amazon.com/dp/${asin}`
          const result = await chrome.runtime.sendMessage({
            action: 'EXTRACT_AND_ASSEMBLE_AMAZON_DATA',
            productUrl: productUrl,
            batchMode: true // 标记为批量模式
          })

          if (result.success) {
            markProductAsCollected(productElement, result.productType)
            collectedData.push({
              asin: asin,
              spuData: result.spuData,
              skuDataList: result.skuDataList,
              dianxiaomiData: result.dianxiaomiData
            })
            return { success: true, asin: asin }
          } else {
            throw new Error(result.error || '数据处理失败')
          }

        } catch (error) {
          console.error(`[AmazonCollector] 采集商品 ${asin} 失败:`, error)
          markProductAsFailed(productElement, error)
          return { success: false, asin: asin, error: error }
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.allSettled(batchPromises)

      // 统计结果
      batchResults.forEach(result => {
        if (result.status === 'fulfilled' && result.value.success) {
          successCount++
          collectedCount++
        } else {
          failCount++
        }
      })

      // 批次间延迟，避免请求过快
      if (i + batchSize < productElements.length) {
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }

    // 关闭进度通知
    if (batchProgressNotification) {
      batchProgressNotification.remove()
    }

    updateProductCount()

    // 显示批量采集结果
    showBatchCollectionResult(successCount, failCount, collectedData)

    // 检查是否有下一页
    if (hasNextPage() && successCount > 0) {
      const shouldContinue = confirm(`当前页面采集完成！成功: ${successCount}, 失败: ${failCount}\n\n是否继续采集下一页？`)
      if (shouldContinue) {
        goToNextPage()
      }
    }

  } catch (error) {
    console.error('[AmazonCollector] 批量采集失败:', error)

    // 关闭进度通知
    if (batchProgressNotification) {
      batchProgressNotification.remove()
    }

    const errorMessage = getDetailedErrorMessage(error)
    showNotification(`批量采集失败: ${errorMessage}`, 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 标记产品采集失败
function markProductAsFailed(productElement: Element, error: any) {
  productElement.classList.add('amazon-failed')
  productElement.setAttribute('style', 'opacity: 0.6; border: 2px solid #ff4d4f;')

  const badge = document.createElement('div')
  badge.style.cssText = `
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    z-index: 10;
    cursor: pointer;
  `
  badge.textContent = '采集失败'
  badge.title = error instanceof Error ? error.message : '未知错误'

  const container = productElement.querySelector('[data-cy="title-recipe-container"], .s-result-item')
  if (container) {
    container.style.position = 'relative'
    container.appendChild(badge)
  }
}

// 显示进度通知
function showProgressNotification(message: string, progress: number): HTMLElement {
  // 移除已存在的进度通知
  const existing = document.querySelector('.amazon-progress-notification')
  if (existing) {
    existing.remove()
  }

  const notification = document.createElement('div')
  notification.className = 'amazon-progress-notification'
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    min-width: 300px;
    font-family: Arial, sans-serif;
  `

  notification.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 8px;">
      <div style="width: 16px; height: 16px; border: 2px solid #ff4d4f; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 8px;"></div>
      <span style="font-weight: 500; color: #333;">${message}</span>
    </div>
    <div style="background: #f5f5f5; border-radius: 4px; height: 6px; overflow: hidden;">
      <div class="progress-bar" style="background: #ff4d4f; height: 100%; width: ${progress}%; transition: width 0.3s ease;"></div>
    </div>
    <div class="progress-text" style="font-size: 12px; color: #666; margin-top: 4px;">${progress}%</div>
  `

  // 添加旋转动画样式
  if (!document.querySelector('#amazon-spinner-style')) {
    const style = document.createElement('style')
    style.id = 'amazon-spinner-style'
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `
    document.head.appendChild(style)
  }

  document.body.appendChild(notification)
  return notification
}

// 更新进度通知
function updateProgressNotification(notification: HTMLElement, message: string, progress: number): void {
  if (!notification) return

  const messageElement = notification.querySelector('span')
  const progressBar = notification.querySelector('.progress-bar') as HTMLElement
  const progressText = notification.querySelector('.progress-text')

  if (messageElement) messageElement.textContent = message
  if (progressBar) progressBar.style.width = `${progress}%`
  if (progressText) progressText.textContent = `${progress}%`
}

// 显示批量采集进度通知
function showBatchProgressNotification(totalCount: number): HTMLElement {
  const existing = document.querySelector('.amazon-batch-progress-notification')
  if (existing) {
    existing.remove()
  }

  const notification = document.createElement('div')
  notification.className = 'amazon-batch-progress-notification'
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    min-width: 350px;
    font-family: Arial, sans-serif;
  `

  notification.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 12px;">
      <div style="width: 20px; height: 20px; border: 3px solid #ff4d4f; border-top: 3px solid transparent; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 12px;"></div>
      <div>
        <div style="font-weight: 600; color: #333; font-size: 14px;">批量采集进行中</div>
        <div class="batch-message" style="font-size: 12px; color: #666; margin-top: 2px;">准备开始采集...</div>
      </div>
    </div>
    <div style="background: #f5f5f5; border-radius: 4px; height: 8px; overflow: hidden; margin-bottom: 8px;">
      <div class="batch-progress-bar" style="background: linear-gradient(90deg, #ff4d4f, #ff7875); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
    </div>
    <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
      <span class="batch-progress-text">0 / ${totalCount}</span>
      <span class="batch-percentage">0%</span>
    </div>
    <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
      <div style="display: flex; justify-content: space-between; font-size: 11px;">
        <span style="color: #52c41a;">✓ 成功: <span class="success-count">0</span></span>
        <span style="color: #ff4d4f;">✗ 失败: <span class="fail-count">0</span></span>
      </div>
    </div>
  `

  document.body.appendChild(notification)
  return notification
}

// 更新批量采集进度
function updateBatchProgressNotification(
  notification: HTMLElement,
  message: string,
  current: number,
  total: number
): void {
  if (!notification) return

  const progress = Math.round((current / total) * 100)

  const messageElement = notification.querySelector('.batch-message')
  const progressBar = notification.querySelector('.batch-progress-bar') as HTMLElement
  const progressText = notification.querySelector('.batch-progress-text')
  const percentageText = notification.querySelector('.batch-percentage')

  if (messageElement) messageElement.textContent = message
  if (progressBar) progressBar.style.width = `${progress}%`
  if (progressText) progressText.textContent = `${current} / ${total}`
  if (percentageText) percentageText.textContent = `${progress}%`
}

// 显示批量采集结果
function showBatchCollectionResult(successCount: number, failCount: number, collectedData: any[]): void {
  const total = successCount + failCount
  const successRate = total > 0 ? Math.round((successCount / total) * 100) : 0

  // 创建结果弹窗
  const modal = document.createElement('div')
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    font-family: Arial, sans-serif;
  `

  modal.innerHTML = `
    <div style="background: white; border-radius: 12px; padding: 24px; max-width: 500px; width: 90%;">
      <div style="text-align: center; margin-bottom: 20px;">
        <div style="font-size: 48px; margin-bottom: 8px;">${successCount > 0 ? '🎉' : '😔'}</div>
        <h3 style="margin: 0; color: #333;">批量采集完成</h3>
      </div>

      <div style="background: #f8f9fa; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; text-align: center;">
          <div>
            <div style="font-size: 24px; font-weight: bold; color: #333;">${total}</div>
            <div style="font-size: 12px; color: #666;">总计</div>
          </div>
          <div>
            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">${successCount}</div>
            <div style="font-size: 12px; color: #666;">成功</div>
          </div>
          <div>
            <div style="font-size: 24px; font-weight: bold; color: #ff4d4f;">${failCount}</div>
            <div style="font-size: 12px; color: #666;">失败</div>
          </div>
        </div>
        <div style="margin-top: 12px; text-align: center;">
          <div style="font-size: 14px; color: #333;">成功率: <strong>${successRate}%</strong></div>
        </div>
      </div>

      ${collectedData.length > 0 ? `
        <div style="margin-bottom: 20px;">
          <h4 style="margin: 0 0 8px 0; color: #333;">采集的商品</h4>
          <div style="max-height: 200px; overflow-y: auto; border: 1px solid #e8e8e8; border-radius: 4px;">
            ${collectedData.slice(0, 10).map(item => `
              <div style="padding: 8px 12px; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 12px; color: #333;">${item.spuData?.title?.substring(0, 40) || item.asin}...</span>
                <span style="font-size: 11px; color: #666;">${item.asin}</span>
              </div>
            `).join('')}
            ${collectedData.length > 10 ? `<div style="padding: 8px 12px; text-align: center; color: #666; font-size: 12px;">还有 ${collectedData.length - 10} 个商品...</div>` : ''}
          </div>
        </div>
      ` : ''}

      <div style="display: flex; gap: 12px; justify-content: center;">
        <button class="batch-result-close-btn"
                style="padding: 8px 16px; border: 1px solid #d9d9d9; border-radius: 6px; background: white; cursor: pointer;">
          关闭
        </button>
        ${collectedData.length > 0 ? `
          <button class="batch-result-download-btn"
                  style="padding: 8px 16px; border: none; border-radius: 6px; background: #1890ff; color: white; cursor: pointer;">
            下载数据
          </button>
        ` : ''}
      </div>
    </div>
  `

  document.body.appendChild(modal)

  // 添加事件监听器
  const closeBtn = modal.querySelector('.batch-result-close-btn')
  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      modal.remove()
    })
  }

  const downloadBtn = modal.querySelector('.batch-result-download-btn')
  if (downloadBtn && collectedData.length > 0) {
    downloadBtn.addEventListener('click', () => {
      const blob = new Blob([JSON.stringify(collectedData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `amazon-batch-collection-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)
    })
  }

  // 自动关闭
  setTimeout(() => {
    if (modal.parentNode) {
      modal.remove()
    }
  }, 30000) // 30秒后自动关闭
}

// 标记产品为已采集
function markProductAsCollected(productElement: Element, productType?: string) {
  productElement.classList.add('amazon-collected')

  // 添加已采集标记
  const badge = document.createElement('div')
  badge.classList.add('amazon-collected-badge')

  // 根据商品类型显示不同的标记
  if (productType === 'multi-variant') {
    badge.textContent = '已采集(多变体)'
    badge.classList.add('amazon-collected-badge-multi')
  } else {
    badge.textContent = '已采集'
  }

  const container = productElement.querySelector('[data-cy="title-recipe-container"], .s-result-item')
  if (container) {
    container.classList.add('amazon-collected-container')
    container.appendChild(badge)
  }
}

// 更新按钮状态
function updateButtonState() {
  const button = document.querySelector('.amazon-batch-collect-btn') as HTMLElement
  if (button) {
    if (isCollecting) {
      button.classList.add('amazon-batch-collect-btn-loading')
      button.classList.remove('amazon-batch-collect-btn-idle')
      const mainText = button.querySelector('span:first-child') as HTMLElement
      if (mainText) {
        mainText.textContent = '采集中...'
      }
    } else {
      button.classList.remove('amazon-batch-collect-btn-loading')
      button.classList.add('amazon-batch-collect-btn-idle')
      const mainText = button.querySelector('span:first-child') as HTMLElement
      if (mainText) {
        mainText.textContent = '批量采集'
      }
    }
  }
}

// 更新商品数量显示
function updateProductCount() {
  const countText = document.querySelector('.product-count') as HTMLElement
  if (countText) {
    const productCount = getMatchedProductCount()
    countText.textContent = `${productCount} 个商品`
  }
}

// 显示数据预览弹窗（增强版，包含推送功能）
function showDataPreviewModal(spuData: Record<string, any>, skuDataList: any[], dianxiaomiData: Record<string, any>) {
  // 移除已存在的弹窗
  const existingModal = document.querySelector('.amazon-data-preview-modal')
  if (existingModal) {
    existingModal.remove()
  }

  // 创建弹窗容器
  const modal = document.createElement('div')
  modal.className = 'amazon-data-preview-modal'
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    font-family: Arial, sans-serif;
  `

  // 创建弹窗内容
  const modalContent = document.createElement('div')
  modalContent.style.cssText = `
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 1000px;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `

  // 创建弹窗HTML内容
  modalContent.innerHTML = createModalContent(spuData, skuDataList, dianxiaomiData)

  modal.appendChild(modalContent)
  document.body.appendChild(modal)

  // 添加事件监听器
  setupModalEventListeners(modal, spuData, skuDataList, dianxiaomiData)
}

// 创建弹窗内容
function createModalContent(spuData: Record<string, any>, skuDataList: any[], dianxiaomiData: Record<string, any>): string {
  // 解析图片URLs
  const getImageUrls = () => {
    try {
      const imageUrls = JSON.parse(spuData.imageUrls || '[]')
      return Array.isArray(imageUrls) ? imageUrls : []
    } catch {
      return []
    }
  }

  // 解析五点描述
  const getBulletPoints = () => {
    try {
      const bulletPoints = JSON.parse(spuData.bulletPoints || '[]')
      return Array.isArray(bulletPoints) ? bulletPoints : []
    } catch {
      return []
    }
  }

  // 解析产品属性
  const getProductAttributes = () => {
    try {
      const attributes = JSON.parse(spuData.productAttributes || '{}')
      return typeof attributes === 'object' ? attributes : {}
    } catch {
      return {}
    }
  }

  const imageUrls = getImageUrls()
  const bulletPoints = getBulletPoints()
  const productAttributes = getProductAttributes()

  return `
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
      <h3 style="margin: 0; color: #333;">Amazon商品数据预览</h3>
      <button class="close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999; padding: 0; width: 30px; height: 30px;">×</button>
    </div>

    <div style="display: flex; border-bottom: 1px solid #eee;">
      <button class="tab-btn active" data-tab="preview" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid #ff4d4f; color: #ff4d4f;">可视化预览</button>
      <button class="tab-btn" data-tab="raw" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent;">原始数据</button>
      <button class="tab-btn" data-tab="formatted" style="padding: 12px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent;">组装数据</button>
    </div>

    <div style="flex: 1; overflow: auto; padding: 20px;">
      <!-- 可视化预览标签页 -->
      <div class="tab-content" id="preview-tab" style="display: block;">
        ${createVisualPreviewContent(spuData, skuDataList, dianxiaomiData, imageUrls, bulletPoints, productAttributes)}
      </div>

      <!-- 原始数据标签页 -->
      <div class="tab-content" id="raw-tab" style="display: none;">
        ${createRawDataContent(spuData, skuDataList)}
      </div>

      <!-- 组装数据标签页 -->
      <div class="tab-content" id="formatted-tab" style="display: none;">
        <h4 style="margin: 0 0 15px 0; color: #333;">店小秘格式数据</h4>
        <pre style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; overflow: auto; max-height: 400px; white-space: pre-wrap; word-break: break-all;">${JSON.stringify(dianxiaomiData, null, 2)}</pre>
      </div>
    </div>

    <div style="display: flex; justify-content: flex-end; gap: 10px; padding: 20px; border-top: 1px solid #eee;">
      <button class="btn-close" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #6c757d; color: white;">关闭</button>
      <button class="btn-copy" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white;">复制JSON</button>
      <button class="btn-download" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #28a745; color: white;">下载JSON</button>
    </div>
  `
}

// 创建可视化预览内容
function createVisualPreviewContent(spuData: Record<string, any>, skuDataList: any[], dianxiaomiData: Record<string, any>, imageUrls: string[], bulletPoints: string[], productAttributes: Record<string, any>): string {
  // 创建图片预览
  const createImagePreview = () => {
    if (imageUrls.length === 0) return '<p style="color: #999;">暂无图片</p>'

    const mainImage = imageUrls[0] || spuData.mainImageUrl
    const thumbnails = imageUrls.slice(1, 6) // 最多显示5个缩略图

    return `
      <div style="margin-bottom: 20px;">
        <h5 style="margin: 0 0 10px 0; color: #333;">商品图片</h5>
        <div style="display: flex; gap: 10px; align-items: flex-start;">
          ${mainImage ? `<img src="${mainImage}" style="width: 150px; height: 150px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;" alt="主图">` : ''}
          <div style="display: flex; flex-direction: column; gap: 5px;">
            ${thumbnails.map(url => `<img src="${url}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd;" alt="缩略图">`).join('')}
          </div>
        </div>
      </div>
    `
  }

  // 创建SKU列表
  const createSkuList = () => {
    if (skuDataList.length === 0) return '<p style="color: #999;">暂无SKU数据</p>'

    return `
      <div style="margin-bottom: 20px;">
        <h5 style="margin: 0 0 10px 0; color: #333;">SKU列表 (${skuDataList.length}个变体)</h5>
        <div style="overflow-x: auto;">
          <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
            <thead>
              <tr style="background: #f8f9fa;">
                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">ASIN</th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">价格</th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">库存状态</th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">变体属性</th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">图片</th>
              </tr>
            </thead>
            <tbody>
              ${skuDataList.map(sku => {
                const attributes = JSON.parse(sku.variationAttributes || '{}')
                const attributeText = Object.entries(attributes).map(([key, value]) => `${key}: ${value}`).join(', ') || '无变体属性'

                return `
                  <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;">${sku.asin}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">$${sku.price || 'N/A'}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">
                      <span style="padding: 2px 6px; border-radius: 3px; font-size: 10px; background: ${sku.stockStatus === 'In Stock' ? '#d4edda' : '#f8d7da'}; color: ${sku.stockStatus === 'In Stock' ? '#155724' : '#721c24'};">
                        ${sku.stockStatus}
                      </span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${attributeText}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">
                      ${sku.imageUrl ? `<img src="${sku.imageUrl}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;" alt="SKU图片">` : 'N/A'}
                    </td>
                  </tr>
                `
              }).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `
  }

  return `
    <h4 style="margin: 0 0 20px 0; color: #333;">📦 商品数据可视化预览</h4>

    <!-- SPU基础信息卡片 -->
    <div style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
      <h5 style="margin: 0 0 15px 0; color: #333;">🏷️ SPU基础信息</h5>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
        <div><label style="font-weight: 500; color: #666; display: block;">商品名称:</label><span style="word-break: break-word;">${spuData.title || '未获取'}</span></div>
        <div><label style="font-weight: 500; color: #666; display: block;">ASIN:</label><span>${spuData.asin || '未获取'}</span></div>
        <div><label style="font-weight: 500; color: #666; display: block;">品牌:</label><span>${spuData.brand || '未获取'}</span></div>
        <div><label style="font-weight: 500; color: #666; display: block;">评分:</label><span>${spuData.rating || '未获取'} ⭐ (${spuData.reviewCount || 0} 评论)</span></div>
        <div><label style="font-weight: 500; color: #666; display: block;">类目路径:</label><span style="word-break: break-word;">${spuData.categoryPath || '未获取'}</span></div>
        <div><label style="font-weight: 500; color: #666; display: block;">图片数量:</label><span>${imageUrls.length} 张</span></div>
      </div>
    </div>

    <!-- 图片预览 -->
    ${createImagePreview()}

    <!-- SKU列表 -->
    ${createSkuList()}

    <!-- 五点描述 -->
    ${bulletPoints.length > 0 ? `
      <div style="margin-bottom: 20px;">
        <h5 style="margin: 0 0 10px 0; color: #333;">📝 产品特点</h5>
        <ul style="margin: 0; padding-left: 20px;">
          ${bulletPoints.map(point => `<li style="margin-bottom: 5px;">${point}</li>`).join('')}
        </ul>
      </div>
    ` : ''}

    <!-- 产品属性 -->
    ${Object.keys(productAttributes).length > 0 ? `
      <div style="margin-bottom: 20px;">
        <h5 style="margin: 0 0 10px 0; color: #333;">🔧 产品属性</h5>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
          ${Object.entries(productAttributes).map(([key, value]) => `
            <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
              <strong style="color: #666;">${key}:</strong> ${value}
            </div>
          `).join('')}
        </div>
      </div>
    ` : ''}

    <!-- 配置信息 -->
    <div style="background: #e3f2fd; border-radius: 8px; padding: 15px;">
      <h5 style="margin: 0 0 10px 0; color: #333;">⚙️ 店小秘配置</h5>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 12px;">
        <div><strong>分类ID:</strong> ${dianxiaomiData.categoryId || '未设置'}</div>
        <div><strong>店铺ID:</strong> ${dianxiaomiData.shopId || '未设置'}</div>
        <div><strong>发布状态:</strong> ${dianxiaomiData.dxmState || '未设置'}</div>
      </div>
    </div>
  `
}

// 创建原始数据内容
function createRawDataContent(spuData: Record<string, any>, skuDataList: any[]): string {
  return `
    <h4 style="margin: 0 0 15px 0; color: #333;">📄 原始数据</h4>

    <div style="margin-bottom: 20px;">
      <h5 style="margin: 0 0 10px 0; color: #333;">SPU数据</h5>
      <pre style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 11px; line-height: 1.4; overflow: auto; max-height: 300px; white-space: pre-wrap; word-break: break-all;">${JSON.stringify(spuData, null, 2)}</pre>
    </div>

    <div style="margin-bottom: 20px;">
      <h5 style="margin: 0 0 10px 0; color: #333;">SKU数据列表 (${skuDataList.length}个)</h5>
      <pre style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 11px; line-height: 1.4; overflow: auto; max-height: 300px; white-space: pre-wrap; word-break: break-all;">${JSON.stringify(skuDataList, null, 2)}</pre>
    </div>
  `
}

// 设置弹窗事件监听器
function setupModalEventListeners(modal: HTMLElement, spuData: Record<string, any>, skuDataList: any[], dianxiaomiData: Record<string, any>) {
  // 关闭按钮
  const closeBtn = modal.querySelector('.close-btn')
  const btnClose = modal.querySelector('.btn-close')

  const closeModal = () => modal.remove()

  closeBtn?.addEventListener('click', closeModal)
  btnClose?.addEventListener('click', closeModal)

  // 点击背景关闭
  modal.addEventListener('click', (e) => {
    if (e.target === modal) closeModal()
  })

  // 标签页切换
  const tabBtns = modal.querySelectorAll('.tab-btn')
  const tabContents = modal.querySelectorAll('.tab-content')

  tabBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      const tabName = btn.getAttribute('data-tab')

      // 更新按钮状态
      tabBtns.forEach(b => {
        b.classList.remove('amazon-tab-active')
      })
      btn.classList.add('amazon-tab-active')

      // 显示对应内容
      tabContents.forEach(content => {
        content.classList.remove('amazon-tab-content-active')
      })
      const targetContent = modal.querySelector(`#${tabName}-tab`) as HTMLElement
      if (targetContent) {
        targetContent.classList.add('amazon-tab-content-active')
      }
    })
  })

  // 复制功能
  const btnCopy = modal.querySelector('.btn-copy')
  btnCopy?.addEventListener('click', async () => {
    try {
      const activeTab = modal.querySelector('.tab-btn[style*="rgb(255, 77, 79)"]')?.getAttribute('data-tab') || 'formatted'
      let dataToCopy

      if (activeTab === 'raw') {
        dataToCopy = { spuData, skuDataList }
      } else {
        dataToCopy = dianxiaomiData
      }

      await navigator.clipboard.writeText(JSON.stringify(dataToCopy, null, 2))
      showNotification('数据已复制到剪贴板', 'success')
    } catch (error) {
      showNotification('复制失败', 'error')
    }
  })

  // 下载功能
  const btnDownload = modal.querySelector('.btn-download')
  btnDownload?.addEventListener('click', () => {
    try {
      const activeTab = modal.querySelector('.tab-btn[style*="rgb(255, 77, 79)"]')?.getAttribute('data-tab') || 'formatted'
      let dataToCopy

      if (activeTab === 'raw') {
        dataToCopy = { spuData, skuDataList }
      } else {
        dataToCopy = dianxiaomiData
      }

      const dataStr = JSON.stringify(dataToCopy, null, 2)
      const blob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = url
      a.download = `amazon-data-${activeTab}-${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      showNotification('文件下载已开始', 'success')
    } catch (error) {
      showNotification('下载失败', 'error')
    }
  })
}

// 显示配置提示弹窗
function showConfigurationPrompt(missingFields: string[]) {
  // 移除已存在的配置提示弹窗
  const existingPrompt = document.querySelector('.amazon-config-prompt-modal')
  if (existingPrompt) {
    existingPrompt.remove()
  }

  // 创建弹窗容器
  const modal = document.createElement('div')
  modal.className = 'amazon-config-prompt-modal'
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    font-family: Arial, sans-serif;
  `

  // 创建弹窗内容
  const modalContent = document.createElement('div')
  modalContent.style.cssText = `
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `

  modalContent.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee; background: #ff4d4f; color: white; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; display: flex; align-items: center; gap: 8px;">
        <span style="font-size: 20px;">⚠️</span>
        配置不完整
      </h3>
      <button class="close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer; color: white; padding: 0; width: 30px; height: 30px;">×</button>
    </div>

    <div style="padding: 20px;">
      <p style="margin: 0 0 15px 0; color: #333; line-height: 1.5;">
        在开始采集Amazon商品之前，您需要先完善以下配置项：
      </p>

      <div style="background: #fff2f0; border: 1px solid #ffccc7; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 10px 0; color: #cf1322; font-size: 14px;">缺少的配置项：</h4>
        <ul style="margin: 0; padding-left: 20px; color: #cf1322;">
          ${missingFields.map(field => `<li style="margin-bottom: 5px;">${field}</li>`).join('')}
        </ul>
      </div>

      <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 10px 0; color: #389e0d; font-size: 14px;">📋 配置说明：</h4>
        <ul style="margin: 0; padding-left: 20px; color: #389e0d; font-size: 13px; line-height: 1.4;">
          <li>店铺账号：选择要推送商品的店小秘店铺</li>
          <li>经营站点：选择商品销售的目标站点</li>
          <li>发货仓库：选择商品的发货仓库</li>
          <li>运费模板：选择适用的运费模板</li>
          <li>商品分类：选择商品所属的分类</li>
          <li>发布状态：选择商品的发布方式</li>
          <li>发货时效：设置商品的发货时间</li>
          <li>核销地区：设置商品的核销地区</li>
        </ul>
      </div>
    </div>

    <div style="display: flex; justify-content: flex-end; gap: 10px; padding: 20px; border-top: 1px solid #eee; background: #fafafa; border-radius: 0 0 8px 8px;">
      <button class="btn-close" style="padding: 8px 16px; border: 1px solid #d9d9d9; border-radius: 4px; cursor: pointer; background: white; color: #666;">稍后配置</button>
      <button class="btn-config" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background: #1890ff; color: white;">立即配置</button>
    </div>
  `

  modal.appendChild(modalContent)
  document.body.appendChild(modal)

  // 添加事件监听器
  const closeBtn = modal.querySelector('.close-btn')
  const btnClose = modal.querySelector('.btn-close')
  const btnConfig = modal.querySelector('.btn-config')

  const closeModal = () => modal.remove()

  closeBtn?.addEventListener('click', closeModal)
  btnClose?.addEventListener('click', closeModal)

  btnConfig?.addEventListener('click', () => {
    // 打开配置页面
    chrome.runtime.sendMessage({ action: 'OPEN_CONFIG_PAGE' })
    closeModal()
  })

  // 点击背景关闭
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeModal()
    }
  })
}

// 显示通知
function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
  // 创建通知元素
  const notification = document.createElement('div')
  notification.classList.add('amazon-notification', `amazon-notification-${type}`)
  notification.textContent = message

  document.body.appendChild(notification)

  // 动画显示
  setTimeout(() => {
    notification.classList.add('amazon-notification-show')
  }, 100)

  // 自动隐藏
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      document.body.removeChild(notification)
    }, 300)
  }, 3000)
}

// 工具函数
function isAmazonSearchPage(): boolean {
  return window.location.href.includes('amazon.com/s') || 
         window.location.href.includes('amazon.com/gp/search')
}

function isAmazonProductPage(): boolean {
  return window.location.href.includes('amazon.com') && 
         (window.location.href.includes('/dp/') || window.location.href.includes('/gp/product/'))
}

function hasNextPage(): boolean {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]')
  return nextPageLink !== null
}

function goToNextPage() {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]') as HTMLAnchorElement
  if (nextPageLink) {
    window.location.href = nextPageLink.href
  }
}

// 注入CSS样式
function injectStyles() {
  if (document.querySelector('#amazon-collector-styles')) return

  const style = document.createElement('style')
  style.id = 'amazon-collector-styles'
  style.textContent = `
    .amazon-collect-btn-hover {
      background: #ff7875 !important;
      transform: scale(1.05) !important;
      box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4) !important;
    }
    .amazon-collected {
      opacity: 0.6 !important;
      border: 2px solid #52c41a !important;
    }
    .amazon-collected-container {
      position: relative !important;
    }
    .amazon-collected-badge {
      position: absolute !important;
      top: 8px !important;
      right: 8px !important;
      background: #52c41a !important;
      color: white !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      font-size: 10px !important;
      z-index: 10 !important;
    }
    .amazon-collected-badge-multi {
      background: #1890ff !important;
    }
    .amazon-batch-collect-btn-loading {
      background: #faad14 !important;
      cursor: not-allowed !important;
    }
    .amazon-batch-collect-btn-idle {
      background: #ff4d4f !important;
      cursor: pointer !important;
    }
    .amazon-tab-active {
      border-bottom-color: #ff4d4f !important;
      color: #ff4d4f !important;
    }
    .amazon-tab-content-active {
      display: block !important;
    }
    .amazon-notification {
      position: fixed !important;
      top: 20px !important;
      right: 20px !important;
      z-index: 10000 !important;
      padding: 12px 16px !important;
      border-radius: 6px !important;
      color: white !important;
      font-size: 14px !important;
      max-width: 300px !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
      transition: all 0.3s ease !important;
      transform: translateX(100%) !important;
    }
    .amazon-notification-show {
      transform: translateX(0) !important;
    }
    .amazon-notification-success {
      background: #52c41a !important;
    }
    .amazon-notification-error {
      background: #ff4d4f !important;
    }
    .amazon-notification-warning {
      background: #faad14 !important;
    }
    .amazon-notification-info {
      background: #1677ff !important;
    }
  `
  document.head.appendChild(style)
}

// 初始化
function init() {
  // 检查是否为Amazon页面
  if (!window.location.href.includes('amazon.com')) {
    return
  }

  console.info('[AmazonCollector] 初始化Amazon采集器...')

  // 注入样式
  injectStyles()

  // 等待页面加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init)
    return
  }

  // 如果是搜索页面，创建批量采集按钮和单品采集按钮
  if (isAmazonSearchPage()) {
    const batchButton = createBatchCollectionButton()
    document.body.appendChild(batchButton)
    addProductCollectionButtons()
  }

  console.info('[AmazonCollector] Amazon采集器初始化完成')
}

// 为产品添加采集按钮
function addProductCollectionButtons() {
  const productElements = document.querySelectorAll('[data-asin]')

  productElements.forEach(productElement => {
    // 避免重复添加
    if (productElement.querySelector('.amazon-product-collect-btn')) {
      return
    }

    // 查找商品图片容器
    const imageContainer = productElement.querySelector('.s-product-image-container, .s-image-container')
    if (imageContainer) {
      // 设置图片容器为相对定位
      const container = imageContainer as HTMLElement
      container.style.position = 'relative'

      const collectButton = createProductCollectionButton(productElement)
      container.appendChild(collectButton)
    }
  })

  // 更新商品数量显示
  updateProductCount()
}

// 启动
init()

// 监听页面变化（SPA路由）
let lastUrl = location.href
new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    setTimeout(init, 1000) // 延迟重新初始化
  }
}).observe(document, { subtree: true, childList: true })
